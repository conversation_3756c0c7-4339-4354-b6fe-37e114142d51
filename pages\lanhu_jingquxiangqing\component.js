Component({
  properties: {},
  data: {
    scenicId: '',
    province: '',
    title: '',
    scenicInfo: null
  },
  lifetimes: {
    created: function () {},
    attached: function () {
      console.info("景区详情页面加载");
      this.getPageParams();
    },
    detached: function () {
      console.info("景区详情页面卸载");
    },
  },
  methods: {
    // 获取页面参数
    getPageParams: function() {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options;

      console.log('接收到的页面参数:', options);

      this.setData({
        scenicId: options.scenicId || '',
        province: options.province || '',
        title: decodeURIComponent(options.title || '')
      });

      // 根据参数加载景区详细信息
      this.loadScenicInfo();
    },

    // 加载景区详细信息
    loadScenicInfo: function() {
      // 这里可以根据scenicId从服务器获取详细信息
      // 目前使用模拟数据
      const mockScenicInfo = {
        id: this.data.scenicId,
        name: this.data.title,
        province: this.data.province,
        description: `这里是${this.data.title}的详细介绍...`,
        images: [],
        rating: 4.8,
        price: '免费',
        openTime: '08:00-18:00'
      };

      this.setData({
        scenicInfo: mockScenicInfo
      });

      console.log('景区信息加载完成:', mockScenicInfo);
    }
  },
});
