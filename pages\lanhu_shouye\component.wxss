.page {
  background-color: rgba(245,244,244,1.000000);
  position: relative;
  width: 750rpx;
  height: 2988rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.group_1 {
  background-image: linear-gradient(195deg, rgba(43,96,172,1.000000) 0, rgba(45,101,176,1.000000) 100.000000%);
  width: 750rpx;
  height: 60rpx;
  display: flex;
  flex-direction: column;
}
.group_2 {
  background-color: rgba(255,255,255,1.000000);
  width: 750rpx;
  height: 586rpx;
  margin-top: 746rpx;
  display: flex;
  flex-direction: column;
  justify-content: flex-center;
}
.section_1 {
  width: 690rpx;
  height: 40rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 30rpx 0 0 28rpx;
}
.image-text_1 {
  width: 172rpx;
  height: 40rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
}
.section_2 {
  background-color: rgba(96,158,254,1.000000);
  width: 34rpx;
  height: 40rpx;
  display: flex;
  flex-direction: column;
}
.text-group_1 {
  width: 96rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Semibold;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_1 {
  width: 56rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-left: 54rpx;
}
.text_2 {
  width: 56rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-left: 54rpx;
}
.text_3 {
  width: 56rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-left: 54rpx;
}
.text_4 {
  width: 56rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-left: 54rpx;
}
.box_1 {
  background-color: rgba(51,51,51,1.000000);
  border-radius: 2rpx;
  width: 24rpx;
  height: 14rpx;
  display: flex;
  flex-direction: column;
  margin: 14rpx 0 0 54rpx;
  transition: transform 0.3s ease;
  cursor: pointer;
}

.box_1:active {
  transform: scale(0.9);
}

/* 省份选择弹窗样式 */
.province-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.province-modal {
  background-color: white;
  border-radius: 24rpx;
  width: 640rpx;
  max-height: 80vh;
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 40rpx 20rpx 40rpx;
  border-bottom: 2rpx solid rgba(0, 0, 0, 0.1);
}

.modal-title {
  font-size: 36rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  color: rgba(0, 0, 0, 1);
}

.modal-close {
  font-size: 48rpx;
  color: rgba(0, 0, 0, 0.6);
  line-height: 36rpx;
  padding: 8rpx;
  transition: color 0.2s ease;
}

.modal-close:active {
  color: rgba(0, 0, 0, 0.8);
}

.modal-content {
  padding: 40rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.province-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.province-item {
  background-color: rgba(248, 250, 251, 1);
  border-radius: 12rpx;
  padding: 20rpx 32rpx;
  border: 2rpx solid transparent;
  transition: all 0.2s ease;
  min-width: 120rpx;
  text-align: center;
}

.province-item.selected {
  background-color: rgba(96, 158, 254, 0.1);
  border-color: rgba(96, 158, 254, 1);
}

.province-item:active {
  transform: scale(0.95);
  background-color: rgba(96, 158, 254, 0.2);
}

.province-text {
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  color: rgba(0, 0, 0, 0.8);
  line-height: 34rpx;
}

.province-item.selected .province-text {
  color: rgba(96, 158, 254, 1);
  font-weight: 500;
}

/* 弹窗动画 */
@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes slideUp {
  0% {
    opacity: 0;
    transform: translateY(100rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.section_3 {
  background-color: rgba(96,158,254,1.000000);
  border-radius: 4rpx;
  width: 46rpx;
  height: 12rpx;
  display: flex;
  flex-direction: column;
  margin: 8rpx 0 0 128rpx;
}
/* 轮播图容器样式 */
.carousel-container {
  width: 690rpx;
  height: 296rpx;
  margin: 50rpx 0 0 30rpx;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
}

.carousel-swiper {
  width: 100%;
  height: 100%;
}

.carousel-item {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.carousel-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

/* 保持原有的image_1样式兼容性 */
.image_1 {
  width: 690rpx;
  height: 296rpx;
}

/* 轮播图信息遮罩 */
.carousel-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  padding: 40rpx 30rpx 30rpx 30rpx;
  color: white;
}

.carousel-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.carousel-title {
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  line-height: 40rpx;
  color: white;
}

.carousel-subtitle {
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  line-height: 32rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 轮播图指示器自定义样式 */
.carousel-swiper .wx-swiper-dots {
  bottom: 20rpx;
}

.carousel-swiper .wx-swiper-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  margin: 0 6rpx;
}

/* 轮播图点击效果 */
.carousel-item:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

/* 轮播图加载状态 */
.carousel-image {
  background-color: rgba(248, 250, 251, 1);
  transition: opacity 0.3s ease;
}

/* 确保轮播图在各种屏幕上都能正常显示 */
@media screen and (max-width: 750rpx) {
  .carousel-container {
    width: calc(100vw - 60rpx);
    margin-left: 30rpx;
    margin-right: 30rpx;
  }
}

/* 轮播图无数据时的样式 */
.carousel-item.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(248, 250, 251, 1);
}

.carousel-item.no-data .carousel-title {
  color: rgba(0, 0, 0, 0.6);
}

.carousel-item.no-data .carousel-subtitle {
  color: rgba(0, 0, 0, 0.4);
}
.section_4 {
  background-color: rgba(248,250,251,1.000000);
  border-radius: 12rpx;
  width: 690rpx;
  height: 90rpx;
  flex-direction: row;
  display: flex;
  margin: 30rpx 0 30rpx 30rpx;
}
.image-text_2 {
  width: 114rpx;
  height: 38rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 26rpx 0 0 22rpx;
}
.thumbnail_1 {
  width: 36rpx;
  height: 38rpx;
}
.text-group_2 {
  width: 60rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(255,154,46,1.000000);
  font-size: 30rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-top: 2rpx;
}
.section_5 {
  background-color: rgba(0,0,0,0.600000);
  border-radius: 100%;
  width: 8rpx;
  height: 8rpx;
  display: flex;
  flex-direction: column;
  margin: 42rpx 0 0 10rpx;
}
.text_5 {
  width: 326rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 26rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 28rpx 0 0 14rpx;
}
.image-text_3 {
  width: 90rpx;
  height: 34rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 28rpx 28rpx 0 78rpx;
}
.text-group_3 {
  width: 48rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.600000);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.thumbnail_2 {
  width: 28rpx;
  height: 28rpx;
  margin-top: 4rpx;
}
.group_3 {
  background-image: linear-gradient(180deg, rgba(255,255,255,1.000000) 0, rgba(255,255,255,1.000000) 100.000000%);
  position: relative;
  width: 750rpx;
  height: 1334rpx;
  margin-top: 30rpx;
  display: flex;
  flex-direction: column;
}
.box_2 {
  width: 750rpx;
  height: 1072rpx;
  background: url(data:image/png;base64,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) 100% no-repeat;
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
}
.group_4 {
  width: 644rpx;
  height: 54rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 40rpx 0 0 30rpx;
}
.box_3 {
  position: relative;
  width: 128rpx;
  height: 54rpx;
  display: flex;
  flex-direction: column;
}
.image_2 {
  width: 104rpx;
  height: 48rpx;
  margin: 6rpx 0 0 8rpx;
}
.text_6 {
  position: absolute;
  left: 0rpx;
  top: 0rpx;
  width: 128rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_7 {
  width: 112rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.500000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-left: 60rpx;
}
.text_8 {
  width: 112rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.500000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-left: 60rpx;
}
.text_9 {
  width: 112rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.500000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-left: 60rpx;
}
/* 城市选择滚动容器 */
.group_5 {
  width: 604rpx;
  height: 48rpx;
  margin: 22rpx 0 0 30rpx;
  white-space: nowrap;
}

/* 城市选择项 */
.city-item {
  display: inline-block;
  background-color: rgba(228,237,255,1.000000);
  border-radius: 8rpx;
  height: 48rpx;
  margin-right: 20rpx;
  padding: 0 32rpx;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  min-width: 100rpx;
  flex-shrink: 0;
}

.city-item.active {
  background-color: rgba(34,34,34,1.000000);
}

.city-item:last-child {
  margin-right: 0;
}

.city-text {
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  color: rgba(0,0,0,0.600000);
  line-height: 34rpx;
  white-space: nowrap;
}

.city-item.active .city-text {
  color: rgba(255,255,255,1.000000);
  font-family: PingFang SC-Medium;
  font-weight: 500;
}

/* 景区卡片样式 */
.scenic-card {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.scenic-card:active {
  transform: scale(0.98);
}

/* 更多按钮点击效果 */
.image-text_3:active {
  opacity: 0.7;
  transform: scale(0.95);
  transition: all 0.1s ease;
}
.text-wrapper_1 {
  background-color: rgba(34,34,34,1.000000);
  border-radius: 8rpx;
  height: 48rpx;
  display: flex;
  flex-direction: column;
  width: 136rpx;
}
.text_10 {
  width: 48rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(255,255,255,1.000000);
  font-size: 24rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 8rpx 0 0 44rpx;
}
.text-wrapper_2 {
  background-color: rgba(228,237,255,1.000000);
  border-radius: 8rpx;
  height: 48rpx;
  margin-left: 20rpx;
  display: flex;
  flex-direction: column;
  width: 136rpx;
}
.text_11 {
  width: 48rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.600000);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 8rpx 0 0 44rpx;
}
.text-wrapper_3 {
  background-color: rgba(228,237,255,1.000000);
  border-radius: 8rpx;
  height: 48rpx;
  margin-left: 20rpx;
  display: flex;
  flex-direction: column;
  width: 136rpx;
}
.text_12 {
  width: 48rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.600000);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 8rpx 0 0 44rpx;
}
.text-wrapper_4 {
  background-color: rgba(228,237,255,1.000000);
  border-radius: 8rpx;
  height: 48rpx;
  margin-left: 20rpx;
  display: flex;
  flex-direction: column;
  width: 136rpx;
}
.text_13 {
  width: 48rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.600000);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 8rpx 0 0 44rpx;
}
.image_3 {
  width: 690rpx;
  height: 314rpx;
  margin: 36rpx 0 0 30rpx;
}
.text-group_4 {
  width: 670rpx;
  height: 86rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin: 30rpx 0 0 30rpx;
}
.text_14 {
  width: 488rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_15 {
  width: 670rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.600000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-top: 20rpx;
}
.group_6 {
  width: 686rpx;
  height: 64rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 20rpx 0 0 30rpx;
}
.text-wrapper_5 {
  background-color: rgba(232,243,255,1.000000);
  border-radius: 4rpx;
  height: 48rpx;
  margin-top: 8rpx;
  display: flex;
  flex-direction: column;
  width: 442rpx;
}
.text_16 {
  width: 400rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(64,128,255,1.000000);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 8rpx 0 0 22rpx;
}
.text-wrapper_6 {
  background-image: linear-gradient(270deg, rgba(245,203,120,1.000000) 0, rgba(245,201,119,1.000000) 100.000000%);
  border-radius: 200rpx;
  height: 64rpx;
  display: flex;
  flex-direction: column;
  width: 172rpx;
}
.text_17 {
  width: 112rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(112,72,19,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Semibold;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 14rpx 0 0 32rpx;
}
.image_4 {
  width: 690rpx;
  height: 314rpx;
  margin: 54rpx 0 10rpx 30rpx;
}
.text-group_5 {
  width: 670rpx;
  height: 86rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin: 40rpx 0 0 30rpx;
}
.text_18 {
  width: 488rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_19 {
  width: 670rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.600000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-top: 20rpx;
}
.box_4 {
  width: 686rpx;
  height: 64rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 20rpx 0 52rpx 30rpx;
}
.text-wrapper_7 {
  background-color: rgba(232,243,255,1.000000);
  border-radius: 4rpx;
  height: 48rpx;
  margin-top: 8rpx;
  display: flex;
  flex-direction: column;
  width: 442rpx;
}
.text_20 {
  width: 400rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(64,128,255,1.000000);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 8rpx 0 0 22rpx;
}
.text-wrapper_8 {
  background-image: linear-gradient(270deg, rgba(245,203,120,1.000000) 0, rgba(245,201,119,1.000000) 100.000000%);
  border-radius: 200rpx;
  height: 64rpx;
  display: flex;
  flex-direction: column;
  width: 172rpx;
}
.text_21 {
  width: 112rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(112,72,19,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Semibold;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 14rpx 0 0 32rpx;
}
.text-wrapper_9 {
  background-color: rgba(228,237,255,1.000000);
  border-radius: 8rpx;
  height: 48rpx;
  display: flex;
  flex-direction: column;
  width: 136rpx;
  position: absolute;
  left: 654rpx;
  top: 116rpx;
}
.text_22 {
  width: 48rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.600000);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 8rpx 0 0 44rpx;
}
.group_7 {
  background-color: rgba(255,255,255,1.000000);
  height: 176rpx;
  margin-top: 56rpx;
  width: 750rpx;
  display: flex;
  flex-direction: column;
}
.section_6 {
  width: 422rpx;
  height: 88rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 36rpx 0 0 164rpx;
}
.image-text_4 {
  width: 48rpx;
  height: 88rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.image_5 {
  width: 42rpx;
  height: 38rpx;
  margin-left: 2rpx;
}
.text-group_6 {
  width: 48rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 24rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 16rpx;
}
.image-text_5 {
  width: 48rpx;
  height: 88rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.thumbnail_3 {
  width: 40rpx;
  height: 40rpx;
  margin-left: 4rpx;
}
.text-group_7 {
  width: 48rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 24rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 14rpx;
}
.section_7 {
  width: 284rpx;
  height: 8rpx;
  display: flex;
  flex-direction: row;
  margin: 26rpx 0 18rpx 230rpx;
}
.box_5 {
  background-color: rgba(0,0,0,1.000000);
  border-radius: 8rpx;
  width: 284rpx;
  height: 8rpx;
  display: flex;
  flex-direction: column;
}
.group_8 {
  background-color: rgba(217,217,217,1.000000);
  height: 774rpx;
  display: flex;
  flex-direction: column;
  width: 750rpx;
  position: absolute;
  left: 0rpx;
  top: 12rpx;
}
.block_1 {
  height: 766rpx;
  background-size: 100% 100%;
  width: 750rpx;
  display: flex;
  flex-direction: column;
}
.image-wrapper_1 {
  width: 656rpx;
  height: 24rpx;
  flex-direction: row;
  display: flex;
  margin: 24rpx 0 0 48rpx;
}
.image_6 {
  width: 54rpx;
  height: 22rpx;
}
.thumbnail_4 {
  width: 34rpx;
  height: 22rpx;
  margin-left: 468rpx;
}
.thumbnail_5 {
  width: 32rpx;
  height: 24rpx;
  margin-left: 10rpx;
}
.image_7 {
  width: 48rpx;
  height: 22rpx;
  margin-left: 10rpx;
}
.box_6 {
  width: 706rpx;
  height: 76rpx;
  flex-direction: row;
  display: flex;
  margin: -735rpx 0 0 30rpx;
}
.box_7 {
  width: 128rpx;
  height: 76rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-top: 60rpx;
}
.text_23 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(255,255,255,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
}
.image-text_6 {
  width: 112rpx;
  height: 28rpx;
  margin-top: 4rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
}
.thumbnail_6 {
  width: 26rpx;
  height: 26rpx;
  margin-top: 2rpx;
}
.text-group_8 {
  width: 76rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgba(255,255,255,1.000000);
  font-size: 20rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20rpx;
}
.box_8 {
  background-color: rgba(255,255,255,0.800000);
  border-radius: 200rpx;
  width: 370rpx;
  height: 64rpx;
  flex-direction: row;
  display: flex;
  margin: 60rpx 0 0 26rpx;
}
.box_9 {
  background-color: rgba(51,51,51,0.800000);
  width: 28rpx;
  height: 28rpx;
  display: flex;
  flex-direction: column;
  margin: 18rpx 0 0 26rpx;
}
.text_24 {
  width: 240rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.500000);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin: 11rpx 52rpx 0 24rpx;
}
.box_10 {
  background-color: rgba(0,0,0,0.150000);
  border-radius: 200rpx;
  width: 154rpx;
  height: 64rpx;
  flex-direction: row;
  display: flex;
  margin: 4rpx 0 0 28rpx;
}
.image_8 {
  width: 44rpx;
  height: 16rpx;
  margin: 24rpx 0 0 18rpx;
}
.group_9 {
  border-radius: 100%;
  height: 36rpx;
  border: 2px solid rgba(255,255,255,1);
  display: flex;
  flex-direction: column;
  width: 36rpx;
  margin: 14rpx 20rpx 0 36rpx;
}
.section_8 {
  background-color: rgba(255,255,255,1.000000);
  border-radius: 100%;
  width: 12rpx;
  height: 12rpx;
  display: flex;
  flex-direction: column;
  margin: 12rpx 0 0 12rpx;
}
.text-wrapper_10 {
  width: 96rpx;
  height: 34rpx;
  display: flex;
  flex-direction: row;
  margin: 350rpx 0 232rpx 202rpx;
}
.text_25 {
  width: 96rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(255,255,255,1.000000);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
}