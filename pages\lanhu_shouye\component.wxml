<view class="page">
  <view class="group_1"></view>
  <view class="group_2">
    <view class="province-section">
      <!-- 主要省份行 -->
      <view class="section_1">
        <view class="province-list-container">
          <view class="image-text_1">
            <image src="/images/dingwei.png" class="section_2"></image>
            <text lines="1" class="{{provinces[0].selected ? 'text-group_1' : ''}}"
                  bindtap="selectProvince"
                  data-index="0">{{provinces[0].name}}</text>
          </view>
          <!-- 显示前4个未选中的省份 -->
          <block wx:for="{{provinces}}" wx:key="name" wx:if="{{index > 0 && index <= 4}}">
            <text lines="1"
                  class="text_{{index}}"
                  bindtap="selectProvince"
                  data-index="{{index}}">{{item.name}}</text>
          </block>
        </view>
        <image src="/images/xiasanjiao.png"
               class="box_1 {{isProvinceExpanded ? 'expanded' : ''}}"
               bindtap="toggleProvinceList"></image>
      </view>
      <!-- 展开的省份行 -->
      <view wx:if="{{isProvinceExpanded}}" class="expanded-provinces">
        <block wx:for="{{provinces}}" wx:key="name">
          <text wx:if="{{index > 4}}" lines="1"
                class="text_expanded"
                bindtap="selectProvince"
                data-index="{{index}}"
                style="animation-delay: {{(index - 5) * 0.1}}s;">{{item.name}}</text>
        </block>
      </view>
    </view>
    <view class="section_3"></view>
    <image src="../../images/lanhu_shouye/FigmaDDSSlicePNG0f0a39259fd7ced310becc1918181999.png" class="image_1"></image>
    <view class="section_4">
      <view class="image-text_2">
        <image src="../../images/lanhu_shouye/45c507e649d74ac891936f830b3e934d_mergeImage.png" class="thumbnail_1"></image>
        <text lines="1" class="text-group_2">资讯</text>
      </view>
      <view class="section_5"></view>
      <text lines="1" class="text_5">山水含福，福地江苏欢迎您~</text>
      <view class="image-text_3">
        <text lines="1" class="text-group_3">更多</text>
        <image src="../../images/lanhu_shouye/FigmaDDSSlicePNGad1ba875626993d892b3784b64e8205e.png" class="thumbnail_2"></image>
      </view>
    </view>
  </view>
  <view class="group_3">
    <view class="box_2">
      <view class="group_4">
        <view class="box_3">
          <image src="../../images/lanhu_shouye/FigmaDDSSlicePNG6fb1635187c60885e6abfd33425d8aaf.png" class="image_2"></image>
          <text lines="1" class="text_6">精选推荐</text>
        </view>
      </view>
      <view class="group_5">
        <view 
          wx:for="{{cityList}}" 
          wx:key="id"
          class="text-wrapper_{{index + 1}} {{selectedCityId === item.id ? 'active' : ''}}"
          bindtap="selectCity"
          data-city-id="{{item.id}}"
        >
          <text lines="1" class="text_{{10 + index}}">{{item.name}}</text>
        </view>
      </view>
      <image src="../../images/lanhu_shouye/FigmaDDSSlicePNG7ad30acb3ae297130d0e6d93dee06a05.png" class="image_3"></image>
      <view class="text-group_4">
        <text lines="1" class="text_14">苏州博物馆|细品苏式风雅贝氏美学</text>
        <text lines="1" class="text_15">走进苏州博物馆，从古典园林到吴地文化知无不言</text>
      </view>
      <view class="group_6">
        <view class="text-wrapper_5">
          <text lines="1" class="text_16">2小时讲解时长丨建筑鉴赏丨国宝物语</text>
        </view>
        <view class="text-wrapper_6">
          <text lines="1" class="text_17">立即购买</text>
        </view>
      </view>
      <image src="../../images/lanhu_shouye/FigmaDDSSlicePNG7ad30acb3ae297130d0e6d93dee06a05.png" class="image_4"></image>
    </view>
    <view class="text-group_5">
      <text lines="1" class="text_18">苏州博物馆|细品苏式风雅贝氏美学</text>
      <text lines="1" class="text_19">走进苏州博物馆，从古典园林到吴地文化知无不言</text>
    </view>
    <view class="box_4">
      <view class="text-wrapper_7">
        <text lines="1" class="text_20">2小时讲解时长丨建筑鉴赏丨国宝物语</text>
      </view>
      <view class="text-wrapper_8">
        <text lines="1" class="text_21">立即购买</text>
      </view>
    </view>
  </view>

  <!-- 顶部导航栏样式 -->
  <view class="group_8">
    <view class="block_1">
      <image src="{{backgroundImage}}" class="block_1" mode="aspectFill"></image>
      <view class="box_6">
        <view class="box_7">
          <view class="location-container">
            <text wx:if="{{isLoading}}" lines="1" class="text_23">定位中...</text>
            <text wx:elif="{{locationFailed}}" lines="1" class="text_23" bindtap="retryGetLocation">定位失败</text>
            <text wx:else lines="1" class="text_23">{{currentAddress}}</text>
          </view>
          <view class="image-text_6">
            <text lines="1" class="text-group_8">{{weather.temp}}</text>
          </view>
        </view>
        <view class="box_8">
          <image src="/images/Vector.png" class="box_9"></image>
          <input lines="1" class="text_24" placeholder="搜索景区/博物馆/讲师"></input>
        </view>
      </view>
    </view>
  </view>
</view>