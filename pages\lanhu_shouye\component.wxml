<view class="page">
  <view class="group_1"></view>
  <view class="group_2">
    <view class="section_1">
      <view class="image-text_1">
        <image src="/images/dingwei.png" class="section_2"></image>
        <text lines="1" class="{{provinces[0].selected ? 'text-group_1' : ''}}"
              bindtap="selectProvince"
              data-index="0">{{provinces[0].name}}省</text>
      </view>
      <!-- 显示前4个未选中的省份 -->
      <block wx:for="{{provinces}}" wx:key="name" wx:if="{{index > 0 && index <= 4}}">
        <text lines="1"
              class="text_{{index}}"
              bindtap="selectProvince"
              data-index="{{index}}">{{item.name}}</text>
      </block>
      <image src="/images/xiasanjiao.png"
             class="box_1"
             bindtap="toggleProvinceModal"></image>
    </view>

    <!-- 省份选择弹窗 -->
    <view wx:if="{{showProvinceModal}}" class="province-modal-overlay" bindtap="closeProvinceModal">
      <view class="province-modal" catchtap="stopPropagation">
        <view class="modal-header">
          <text class="modal-title">选择省份</text>
          <text class="modal-close" bindtap="closeProvinceModal">×</text>
        </view>
        <view class="modal-content">
          <view class="province-grid">
            <block wx:for="{{provinces}}" wx:key="name">
              <view class="province-item {{item.selected ? 'selected' : ''}}"
                    bindtap="selectProvinceFromModal"
                    data-index="{{index}}">
                <text class="province-text">{{item.name}}</text>
              </view>
            </block>
          </view>
        </view>
      </view>
    </view>
    <view class="section_3"></view>

    <!-- 轮播图容器 -->
    <view class="carousel-container">
      <swiper
        class="carousel-swiper"
        indicator-dots="{{currentCarouselData.length > 1}}"
        indicator-color="rgba(255,255,255,0.5)"
        indicator-active-color="rgba(255,255,255,0.9)"
        autoplay="{{carouselAutoPlay && currentCarouselData.length > 1}}"
        interval="{{carouselInterval}}"
        duration="500"
        circular="{{currentCarouselData.length > 1}}"
        current="{{currentCarouselIndex}}"
        bindchange="onCarouselChange"
        bindtouchstart="onCarouselTouchStart"
        bindtouchend="onCarouselTouchEnd">

        <block wx:for="{{currentCarouselData}}" wx:key="id">
          <swiper-item>
            <view class="carousel-item" bindtap="onCarouselImageTap" data-index="{{index}}">
              <image
                src="{{item.image}}"
                class="image_1 carousel-image"
                mode="aspectFill"
                lazy-load="true">
              </image>

              <!-- 图片信息遮罩 -->
              <view class="carousel-overlay">
                <view class="carousel-info">
                  <text class="carousel-title">{{item.title}}</text>
                  <text class="carousel-subtitle">{{item.subtitle}}</text>
                </view>
              </view>
            </view>
          </swiper-item>
        </block>

        <!-- 默认图片（当没有轮播数据时显示） -->
        <swiper-item wx:if="{{currentCarouselData.length === 0}}">
          <view class="carousel-item">
            <image
              src="../../images/lanhu_shouye/FigmaDDSSlicePNG0f0a39259fd7ced310becc1918181999.png"
              class="image_1 carousel-image"
              mode="aspectFill">
            </image>
            <view class="carousel-overlay">
              <view class="carousel-info">
                <text class="carousel-title">精彩景区</text>
                <text class="carousel-subtitle">发现更多美好</text>
              </view>
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>
    <view class="section_4">
      <view class="image-text_2">
        <image src="../../images/lanhu_shouye/45c507e649d74ac891936f830b3e934d_mergeImage.png" class="thumbnail_1"></image>
        <text lines="1" class="text-group_2">资讯</text>
      </view>
      <view class="section_5"></view>
      <text lines="1" class="text_5">{{currentWelcomeMessage}}</text>
      <view class="image-text_3" bindtap="onMoreButtonTap">
        <text lines="1" class="text-group_3">更多</text>
        <image src="../../images/lanhu_shouye/FigmaDDSSlicePNGad1ba875626993d892b3784b64e8205e.png" class="thumbnail_2"></image>
      </view>
    </view>
  </view>
  <view class="group_3">
    <view class="box_2">
      <view class="group_4">
        <view class="box_3">
          <image src="../../images/lanhu_shouye/FigmaDDSSlicePNG6fb1635187c60885e6abfd33425d8aaf.png" class="image_2"></image>
          <text lines="1" class="text_6">精选推荐</text>
        </view>
      </view>
      <scroll-view class="group_5" scroll-x="true" enable-flex="true">
        <view
          wx:for="{{cityList}}"
          wx:key="id"
          class="city-item {{selectedCityId === item.id ? 'active' : ''}}"
          bindtap="selectCity"
          data-city-id="{{item.id}}"
        >
          <text lines="1" class="city-text">{{item.name}}</text>
        </view>
      </scroll-view>
      <!-- 第一个景区卡片 -->
      <view class="scenic-card" bindtap="onScenicCardTap">
        <image src="{{currentScenicInfo.image}}" class="image_3"></image>
        <view class="text-group_4">
          <text lines="1" class="text_14">{{currentScenicInfo.title}}</text>
          <text lines="1" class="text_15">{{currentScenicInfo.subtitle}}</text>
        </view>
        <view class="group_6">
          <view class="text-wrapper_5">
            <text lines="1" class="text_16">{{currentScenicInfo.description}}</text>
          </view>
          <view class="text-wrapper_6">
            <text lines="1" class="text_17">立即购买</text>
          </view>
        </view>
      </view>

      <!-- 第二个景区卡片 -->
      <view class="scenic-card" bindtap="onScenicCardTap">
        <image src="{{currentScenicInfo.image}}" class="image_4"></image>
      </view>
    </view>
    <view class="text-group_5">
      <text lines="1" class="text_18">{{currentScenicInfo.title}}</text>
      <text lines="1" class="text_19">{{currentScenicInfo.subtitle}}</text>
    </view>
    <view class="box_4">
      <view class="text-wrapper_7">
        <text lines="1" class="text_20">{{currentScenicInfo.description}}</text>
      </view>
      <view class="text-wrapper_8" bindtap="onScenicCardTap">
        <text lines="1" class="text_21">立即购买</text>
      </view>
    </view>
  </view>

  <!-- 顶部导航栏样式 -->
  <view class="group_8">
    <view class="block_1">
      <image src="{{backgroundImage}}" class="block_1" mode="aspectFill"></image>
      <view class="box_6">
        <view class="box_7">
          <view class="location-container">
            <text wx:if="{{isLoading}}" lines="1" class="text_23">定位中...</text>
            <text wx:elif="{{locationFailed}}" lines="1" class="text_23" bindtap="retryGetLocation">定位失败</text>
            <text wx:else lines="1" class="text_23">{{currentAddress}}</text>
          </view>
          <view class="image-text_6">
            <text lines="1" class="text-group_8">{{weather.temp}}</text>
          </view>
        </view>
        <view class="box_8">
          <image src="/images/Vector.png" class="box_9"></image>
          <input lines="1" class="text_24" placeholder="搜索景区/博物馆/讲师"></input>
        </view>
      </view>
    </view>
  </view>
</view>