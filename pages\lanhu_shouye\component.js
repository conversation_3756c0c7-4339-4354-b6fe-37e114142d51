
Component({
  properties: {},
  data: {
    currentAddress: '定位中...',
    isLoading: true,
    locationFailed: false,
    weather: {
      temp: '--',
      weather: '--'
    },
    backgroundImage: '/images/lanhu_shouye/FigmaDDSSlicePNG6878c0806cc44a4dc399352e0e756437.png',
    cityList: [
      { id: 0, name: '全部' },
      { id: 1, name: '南京' },
      { id: 2, name: '苏州' },
      { id: 3, name: '无锡' }
    ],
    selectedCityId: 0,
    provinces: [
      { name: '江苏', selected: true },
      { name: '陕西', selected: false },
      { name: '四川', selected: false },
      { name: '北京', selected: false },
      { name: '河南', selected: false },
      { name: '浙江', selected: false },
      { name: '广东', selected: false },
      { name: '山东', selected: false },
      { name: '湖北', selected: false },
      { name: '湖南', selected: false }
    ],
    selectedProvinceIndex: 0,
    showProvinceModal: false,  // 省份选择弹窗是否显示

    // 轮播图相关数据
    currentCarouselIndex: 0,  // 当前轮播图索引
    carouselAutoPlay: true,   // 是否自动播放
    carouselInterval: 3000,   // 自动播放间隔(毫秒)
    carouselTimer: null,      // 轮播定时器

    // 省份对应的轮播图数据
    provinceCarouselData: {
      '江苏': [
        {
          id: 1,
          image: '../../images/lanhu_shouye/FigmaDDSSlicePNG0f0a39259fd7ced310becc1918181999.png',
          title: '苏州园林',
          subtitle: '江南水乡的经典代表',
          scenicId: 'suzhou_garden'
        },
        {
          id: 2,
          image: '../../images/lanhu_shouye/FigmaDDSSlicePNG7ad30acb3ae297130d0e6d93dee06a05.png',
          title: '南京夫子庙',
          subtitle: '六朝古都的文化印记',
          scenicId: 'nanjing_fuzimiao'
        },
        {
          id: 3,
          image: '../../images/lanhu_shouye/FigmaDDSSlicePNG0f0a39259fd7ced310becc1918181999.png',
          title: '无锡太湖',
          subtitle: '烟波浩渺的太湖美景',
          scenicId: 'wuxi_taihu'
        }
      ],
      '陕西': [
        {
          id: 4,
          image: '../../images/lanhu_shouye/FigmaDDSSlicePNG7ad30acb3ae297130d0e6d93dee06a05.png',
          title: '兵马俑',
          subtitle: '世界第八大奇迹',
          scenicId: 'xian_bingmayong'
        },
        {
          id: 5,
          image: '../../images/lanhu_shouye/FigmaDDSSlicePNG0f0a39259fd7ced310becc1918181999.png',
          title: '华山',
          subtitle: '奇险天下第一山',
          scenicId: 'huashan'
        }
      ],
      '四川': [
        {
          id: 6,
          image: '../../images/lanhu_shouye/FigmaDDSSlicePNG0f0a39259fd7ced310becc1918181999.png',
          title: '九寨沟',
          subtitle: '人间仙境的童话世界',
          scenicId: 'jiuzhaigou'
        },
        {
          id: 7,
          image: '../../images/lanhu_shouye/FigmaDDSSlicePNG7ad30acb3ae297130d0e6d93dee06a05.png',
          title: '峨眉山',
          subtitle: '佛教名山金顶云海',
          scenicId: 'emeishan'
        }
      ],
      '北京': [
        {
          id: 8,
          image: '../../images/lanhu_shouye/FigmaDDSSlicePNG7ad30acb3ae297130d0e6d93dee06a05.png',
          title: '故宫',
          subtitle: '明清皇家宫殿建筑群',
          scenicId: 'beijing_gugong'
        },
        {
          id: 9,
          image: '../../images/lanhu_shouye/FigmaDDSSlicePNG0f0a39259fd7ced310becc1918181999.png',
          title: '长城',
          subtitle: '万里长城永不倒',
          scenicId: 'beijing_changcheng'
        }
      ],
      '河南': [
        {
          id: 10,
          image: '../../images/lanhu_shouye/FigmaDDSSlicePNG0f0a39259fd7ced310becc1918181999.png',
          title: '少林寺',
          subtitle: '禅宗祖庭武术圣地',
          scenicId: 'shaolin_temple'
        }
      ],
      '浙江': [
        {
          id: 11,
          image: '../../images/lanhu_shouye/FigmaDDSSlicePNG7ad30acb3ae297130d0e6d93dee06a05.png',
          title: '西湖',
          subtitle: '上有天堂下有苏杭',
          scenicId: 'hangzhou_xihu'
        }
      ],
      '广东': [
        {
          id: 12,
          image: '../../images/lanhu_shouye/FigmaDDSSlicePNG0f0a39259fd7ced310becc1918181999.png',
          title: '丹霞山',
          subtitle: '中国红石公园',
          scenicId: 'danxiashan'
        }
      ],
      '山东': [
        {
          id: 13,
          image: '../../images/lanhu_shouye/FigmaDDSSlicePNG7ad30acb3ae297130d0e6d93dee06a05.png',
          title: '泰山',
          subtitle: '五岳之首东岳泰山',
          scenicId: 'taishan'
        }
      ],
      '湖北': [
        {
          id: 14,
          image: '../../images/lanhu_shouye/FigmaDDSSlicePNG0f0a39259fd7ced310becc1918181999.png',
          title: '黄鹤楼',
          subtitle: '江南三大名楼之一',
          scenicId: 'huanghelou'
        }
      ],
      '湖南': [
        {
          id: 15,
          image: '../../images/lanhu_shouye/FigmaDDSSlicePNG7ad30acb3ae297130d0e6d93dee06a05.png',
          title: '张家界',
          subtitle: '奇峰异石的自然奇观',
          scenicId: 'zhangjiajie'
        }
      ]
    },

    // 当前省份的轮播图数据
    currentCarouselData: [],

    // 省份欢迎语数据
    provinceWelcomeMessages: {
      '江苏': '山水含福，福地江苏欢迎您~',
      '陕西': '千年古都，魅力陕西欢迎您~',
      '四川': '天府之国，美丽四川欢迎您~',
      '北京': '古都风韵，首都北京欢迎您~',
      '河南': '华夏之源，中原河南欢迎您~',
      '浙江': '诗画江南，秀美浙江欢迎您~',
      '广东': '改革前沿，活力广东欢迎您~',
      '山东': '齐鲁大地，好客山东欢迎您~',
      '湖北': '千湖之省，灵秀湖北欢迎您~',
      '湖南': '湘楚文化，魅力湖南欢迎您~'
    },

    // 当前欢迎语
    currentWelcomeMessage: '山水含福，福地江苏欢迎您~',

    // 省份对应的城市数据
    provinceCityData: {
      '江苏': [
        { id: 0, name: '全部' },
        { id: 1, name: '南京' },
        { id: 2, name: '苏州' },
        { id: 3, name: '无锡' },
        { id: 4, name: '常州' },
        { id: 5, name: '镇江' },
        { id: 6, name: '扬州' }
      ],
      '陕西': [
        { id: 0, name: '全部' },
        { id: 1, name: '西安' },
        { id: 2, name: '宝鸡' },
        { id: 3, name: '咸阳' },
        { id: 4, name: '渭南' }
      ],
      '四川': [
        { id: 0, name: '全部' },
        { id: 1, name: '成都' },
        { id: 2, name: '绵阳' },
        { id: 3, name: '德阳' },
        { id: 4, name: '乐山' }
      ],
      '北京': [
        { id: 0, name: '全部' },
        { id: 1, name: '东城区' },
        { id: 2, name: '西城区' },
        { id: 3, name: '朝阳区' },
        { id: 4, name: '海淀区' }
      ],
      '河南': [
        { id: 0, name: '全部' },
        { id: 1, name: '郑州' },
        { id: 2, name: '洛阳' },
        { id: 3, name: '开封' }
      ],
      '浙江': [
        { id: 0, name: '全部' },
        { id: 1, name: '杭州' },
        { id: 2, name: '宁波' },
        { id: 3, name: '温州' }
      ],
      '广东': [
        { id: 0, name: '全部' },
        { id: 1, name: '广州' },
        { id: 2, name: '深圳' },
        { id: 3, name: '珠海' }
      ],
      '山东': [
        { id: 0, name: '全部' },
        { id: 1, name: '济南' },
        { id: 2, name: '青岛' },
        { id: 3, name: '烟台' }
      ],
      '湖北': [
        { id: 0, name: '全部' },
        { id: 1, name: '武汉' },
        { id: 2, name: '宜昌' },
        { id: 3, name: '襄阳' }
      ],
      '湖南': [
        { id: 0, name: '全部' },
        { id: 1, name: '长沙' },
        { id: 2, name: '张家界' },
        { id: 3, name: '岳阳' }
      ]
    },

    // 城市对应的景区数据
    cityScenicData: {
      // 江苏省景区数据
      '全部': {
        title: '精选推荐景区',
        subtitle: '发现更多精彩旅程',
        description: '2小时讲解时长丨建筑鉴赏丨文化体验',
        image: '../../images/lanhu_shouye/FigmaDDSSlicePNG7ad30acb3ae297130d0e6d93dee06a05.png',
        scenicId: 'all_scenic'
      },
      '南京': {
        title: '中山陵|缅怀伟人追忆历史',
        subtitle: '走进中山陵，感受民国风云和伟人精神',
        description: '3小时讲解时长丨历史文化丨建筑艺术',
        image: '../../images/lanhu_shouye/FigmaDDSSlicePNG7ad30acb3ae297130d0e6d93dee06a05.png',
        scenicId: 'nanjing_zhongshaling'
      },
      '苏州': {
        title: '苏州博物馆|细品苏式风雅贝氏美学',
        subtitle: '走进苏州博物馆，从古典园林到吴地文化知无不言',
        description: '2小时讲解时长丨建筑鉴赏丨国宝物语',
        image: '../../images/lanhu_shouye/FigmaDDSSlicePNG7ad30acb3ae297130d0e6d93dee06a05.png',
        scenicId: 'suzhou_museum'
      },
      '无锡': {
        title: '太湖风光|烟波浩渺水天一色',
        subtitle: '游览太湖美景，体验江南水乡的独特魅力',
        description: '2.5小时讲解时长丨自然风光丨文化体验',
        image: '../../images/lanhu_shouye/FigmaDDSSlicePNG0f0a39259fd7ced310becc1918181999.png',
        scenicId: 'wuxi_taihu'
      },
      // 陕西省景区数据
      '西安': {
        title: '兵马俑|世界第八大奇迹',
        subtitle: '探索秦始皇陵兵马俑，感受千年历史的震撼',
        description: '3小时讲解时长丨历史文化丨考古发现',
        image: '../../images/lanhu_shouye/FigmaDDSSlicePNG0f0a39259fd7ced310becc1918181999.png',
        scenicId: 'xian_bingmayong'
      },
      '宝鸡': {
        title: '法门寺|佛教圣地千年古刹',
        subtitle: '参观法门寺，感受佛教文化的深厚底蕴',
        description: '2小时讲解时长丨宗教文化丨建筑艺术',
        image: '../../images/lanhu_shouye/FigmaDDSSlicePNG7ad30acb3ae297130d0e6d93dee06a05.png',
        scenicId: 'baoji_famensi'
      },
      // 四川省景区数据
      '成都': {
        title: '宽窄巷子|老成都的慢生活',
        subtitle: '漫步宽窄巷子，体验成都的悠闲时光',
        description: '1.5小时讲解时长丨民俗文化丨美食体验',
        image: '../../images/lanhu_shouye/FigmaDDSSlicePNG0f0a39259fd7ced310becc1918181999.png',
        scenicId: 'chengdu_kuanzaixiangzi'
      },
      // 北京景区数据
      '东城区': {
        title: '故宫博物院|紫禁城的辉煌',
        subtitle: '探索明清皇宫，感受中华文明的璀璨',
        description: '4小时讲解时长丨皇家建筑丨文物珍藏',
        image: '../../images/lanhu_shouye/FigmaDDSSlicePNG7ad30acb3ae297130d0e6d93dee06a05.png',
        scenicId: 'beijing_gugong'
      }
    },

    // 当前景区信息
    currentScenicInfo: {
      title: '苏州博物馆|细品苏式风雅贝氏美学',
      subtitle: '走进苏州博物馆，从古典园林到吴地文化知无不言',
      description: '2小时讲解时长丨建筑鉴赏丨国宝物语',
      image: '../../images/lanhu_shouye/FigmaDDSSlicePNG7ad30acb3ae297130d0e6d93dee06a05.png',
      scenicId: 'suzhou_museum'
    },

    // 当前城市列表
    cityList: [
      { id: 0, name: '全部' },
      { id: 1, name: '南京' },
      { id: 2, name: '苏州' },
      { id: 3, name: '无锡' },
      { id: 4, name: '常州' },
      { id: 5, name: '镇江' },
      { id: 6, name: '扬州' }
    ],

    // 当前选中的城市ID
    selectedCityId: 0
  },

  lifetimes: {
    attached: function() {
      this.getCurrentLocation();
      this.initCarousel();
    },

    detached: function() {
      // 清除轮播定时器
      if (this.data.carouselTimer) {
        clearInterval(this.data.carouselTimer);
      }
    }
  },

  methods: {
    getCurrentLocation: function() {
      this.setData({ isLoading: true, locationFailed: false });
      
      wx.getLocation({
        type: 'gcj02',
        success: res => {
          const latitude = res.latitude;
          const longitude = res.longitude;
          this.getWeatherInfo(latitude, longitude);
          this.getAddressFromCoords(latitude, longitude)
            .then(result => {
              // 从返回的结果中提取需要的地址信息
              console.log(result)
              const address = result.address_component.province;
              this.setData({
                currentAddress: address,
                isLoading: false
              });
            })
            .catch(error => {
              console.error('获取地址失败:', error);
              this.setData({
                currentAddress: '获取地址失败',
                isLoading: false,
                locationFailed: true
              });
              wx.showToast({
                title: '获取地址失败，请检查网络后重试',
                icon: 'none',
                duration: 2000
              });
            });
        },
        fail: (err) => {
          console.error('获取位置失败:', err);
          this.setData({
            currentAddress: '获取位置失败',
            isLoading: false,
            locationFailed: true
          });
          // 判断是否是因为用户拒绝授权导致的失败
          if (err.errMsg.includes('auth deny')) {
            wx.showModal({
              title: '提示',
              content: '需要您的位置权限才能为您提供更好的服务，是否前往设置打开权限？',
              success: (res) => {
                if (res.confirm) {
                  wx.openSetting({
                    success: (settingRes) => {
                      if (settingRes.authSetting['scope.userLocation']) {
                        this.getCurrentLocation();
                      }
                    }
                  });
                }
              }
            });
          } else {
            wx.showToast({
              title: '获取位置失败，请检查网络后重试',
              icon: 'none',
              duration: 2000
            });
          }
        }
      });
    },

    getAddressFromCoords: function(latitude, longitude) {
      return new Promise((resolve, reject) => {
        wx.request({
          url: 'https://apis.map.qq.com/ws/geocoder/v1/',
          data: {
            location: `${latitude},${longitude}`,
            key: 'YE4BZ-SCTLT-OWMXO-V5SQP-LHAT7-PEBFS',
            get_poi: 0
          },
          success: res => {
            if (res.data && res.data.status === 0) {
              resolve(res.data.result);
            } else {
              reject(new Error(res.data.message || '获取地址信息失败'));
            }
          },
          fail: err => {
            reject(err);
          }
        });
      });
    },

    // 获取天气信息
    getWeatherInfo: function(latitude, longitude) {
      wx.request({
        url: 'https://apis.map.qq.com/ws/weather/v1/',
        data: {
          location: `${latitude},${longitude}`,
          key: 'YE4BZ-SCTLT-OWMXO-V5SQP-LHAT7-PEBFS'
        },
        success: res => {
          console.log('天气API响应:', res.data);
          if (res.data && res.data.status === 0 && res.data.result && res.data.result.realtime && res.data.result.realtime.length > 0) {
            const weatherInfo = res.data.result.realtime[0].infos;
            this.setData({
              weather: {
                temp: `${weatherInfo.temperature}℃`,
                weather: weatherInfo.weather
              }
            });
            console.log('天气信息获取成功:', weatherInfo);
          } else {
            console.error('获取天气失败:', res.data);
            this.setData({
              weather: {
                temp: '--℃',
                weather: '--'
              }
            });
          }
        },
        fail: err => {
          console.error('天气API请求失败:', err);
          this.setData({
            weather: {
              temp: '--℃',
              weather: '--'
            }
          });
        }
      });
    },

    // 重试获取位置
    retryGetLocation: function() {
      if (!this.data.isLoading) {
        this.getCurrentLocation();
      }
    },

    // 选择城市
    selectCity: function(e) {
      const cityId = e.currentTarget.dataset.cityId;
      this.setData({
        selectedCityId: cityId
      });
      
      // 获取选中的城市信息
      const selectedCity = this.data.cityList.find(city => city.id === cityId);
      console.log('选中城市:', selectedCity.name);
      
      // 这里可以添加选择城市后的其他操作
      // 例如：更新列表数据、发起请求等
    },
    
    // 选择省份
    selectProvince: function(e) {
      const index = e.currentTarget.dataset.index;
      const provinces = [...this.data.provinces];
      
      // 如果点击的不是第一个省份，则进行位置交换
      if (index !== 0) {
        // 保存点击的省份
        const clickedProvince = { ...provinces[index] };
        // 保存原第一个省份
        const firstProvince = { ...provinces[0] };
        
        // 更新选中状态
        clickedProvince.selected = true;
        firstProvince.selected = false;
        
        // 交换位置
        provinces[0] = clickedProvince;
        provinces[index] = firstProvince;
      }
      
      this.setData({
        provinces,
        selectedProvinceIndex: 0  // 选中的始终是第一个位置
      });

      console.log('选中省份:', provinces[0].name);

      // 更新轮播图数据和相关数据
      this.updateCarouselData(provinces[0].name);
      this.updateProvinceRelatedData(provinces[0].name);
    },

    // 打开省份选择弹窗
    toggleProvinceModal: function() {
      this.setData({
        showProvinceModal: true
      });
      console.log('打开省份选择弹窗');
    },

    // 关闭省份选择弹窗
    closeProvinceModal: function() {
      this.setData({
        showProvinceModal: false
      });
      console.log('关闭省份选择弹窗');
    },

    // 阻止事件冒泡
    stopPropagation: function() {
      // 阻止点击弹窗内容时关闭弹窗
    },

    // 从弹窗中选择省份
    selectProvinceFromModal: function(e) {
      const index = e.currentTarget.dataset.index;
      const provinces = [...this.data.provinces];

      // 如果点击的不是第一个省份，则进行位置交换
      if (index !== 0) {
        // 保存点击的省份
        const clickedProvince = { ...provinces[index] };
        // 保存原第一个省份
        const firstProvince = { ...provinces[0] };

        // 更新选中状态
        clickedProvince.selected = true;
        firstProvince.selected = false;

        // 交换位置
        provinces[0] = clickedProvince;
        provinces[index] = firstProvince;
      }

      this.setData({
        provinces,
        selectedProvinceIndex: 0,
        showProvinceModal: false  // 选择后关闭弹窗
      });

      console.log('从弹窗选中省份:', provinces[0].name);

      // 更新轮播图数据和相关数据
      this.updateCarouselData(provinces[0].name);
      this.updateProvinceRelatedData(provinces[0].name);
    },

    // 初始化轮播图
    initCarousel: function() {
      const currentProvince = this.data.provinces[0].name;
      console.log('初始化轮播图，当前省份:', currentProvince);
      this.updateCarouselData(currentProvince);
      this.updateProvinceRelatedData(currentProvince);
    },

    // 更新轮播图数据
    updateCarouselData: function(provinceName) {
      const carouselData = this.data.provinceCarouselData[provinceName] || [];
      this.setData({
        currentCarouselData: carouselData,
        currentCarouselIndex: 0
      });

      // 重新启动自动播放
      this.startCarouselAutoPlay();
      console.log('更新轮播图数据:', provinceName, carouselData);
    },

    // 开始自动播放
    startCarouselAutoPlay: function() {
      // 清除之前的定时器
      if (this.data.carouselTimer) {
        clearInterval(this.data.carouselTimer);
      }

      // 如果数据少于2个，不启动自动播放
      if (!this.data.carouselAutoPlay || this.data.currentCarouselData.length < 2) {
        return;
      }

      const timer = setInterval(() => {
        this.nextCarouselImage();
      }, this.data.carouselInterval);

      this.setData({
        carouselTimer: timer
      });
    },

    // 停止自动播放
    stopCarouselAutoPlay: function() {
      if (this.data.carouselTimer) {
        clearInterval(this.data.carouselTimer);
        this.setData({
          carouselTimer: null
        });
      }
    },

    // 下一张图片
    nextCarouselImage: function() {
      const currentIndex = this.data.currentCarouselIndex;
      const totalImages = this.data.currentCarouselData.length;

      if (totalImages === 0) return;

      const nextIndex = (currentIndex + 1) % totalImages;
      this.setData({
        currentCarouselIndex: nextIndex
      });
    },

    // 上一张图片
    prevCarouselImage: function() {
      const currentIndex = this.data.currentCarouselIndex;
      const totalImages = this.data.currentCarouselData.length;

      if (totalImages === 0) return;

      const prevIndex = currentIndex === 0 ? totalImages - 1 : currentIndex - 1;
      this.setData({
        currentCarouselIndex: prevIndex
      });
    },

    // 轮播图点击事件
    onCarouselImageTap: function(e) {
      const index = e.currentTarget.dataset.index || this.data.currentCarouselIndex;
      const imageData = this.data.currentCarouselData[index];

      if (!imageData) return;

      // 跳转到景区详情页面
      wx.navigateTo({
        url: `/pages/lanhu_jingquxiangqing/component?scenicId=${imageData.scenicId}&province=${this.data.provinces[0].name}&title=${imageData.title}`,
        success: () => {
          console.log('跳转到景区详情页面:', imageData);
        },
        fail: (err) => {
          console.error('跳转失败:', err);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    },

    // 轮播图滑动事件
    onCarouselChange: function(e) {
      const current = e.detail.current;
      this.setData({
        currentCarouselIndex: current
      });

      // 重新启动自动播放
      this.startCarouselAutoPlay();
    },

    // 轮播图触摸开始
    onCarouselTouchStart: function() {
      this.stopCarouselAutoPlay();
    },

    // 轮播图触摸结束
    onCarouselTouchEnd: function() {
      this.startCarouselAutoPlay();
    },

    // 更新省份相关数据
    updateProvinceRelatedData: function(provinceName) {
      // 更新欢迎语
      const welcomeMessage = this.data.provinceWelcomeMessages[provinceName] || '欢迎您的到来~';

      // 更新城市列表
      const cityList = this.data.provinceCityData[provinceName] || [{ id: 0, name: '全部' }];

      this.setData({
        currentWelcomeMessage: welcomeMessage,
        cityList: cityList,
        selectedCityId: 0  // 重置为"全部"
      });

      // 更新景区信息
      this.updateScenicInfo('全部');

      console.log('更新省份相关数据:', provinceName, {
        welcomeMessage,
        cityCount: cityList.length
      });
    },

    // 更新景区信息
    updateScenicInfo: function(cityName) {
      const scenicInfo = this.data.cityScenicData[cityName] || this.data.cityScenicData['全部'];

      this.setData({
        currentScenicInfo: scenicInfo
      });

      console.log('更新景区信息:', cityName, scenicInfo);
    },

    // 更新城市选择方法
    selectCity: function(e) {
      const cityId = e.currentTarget.dataset.cityId;
      this.setData({
        selectedCityId: cityId
      });

      // 获取选中的城市信息
      const selectedCity = this.data.cityList.find(city => city.id === cityId);
      console.log('选中城市:', selectedCity.name);

      // 更新景区信息
      this.updateScenicInfo(selectedCity.name);
    },

    // "更多"按钮点击事件
    onMoreButtonTap: function() {
      const currentProvince = this.data.provinces[0].name;
      const currentCity = this.data.cityList.find(city => city.id === this.data.selectedCityId);

      // 跳转到更多页面，传递省份和城市参数
      wx.navigateTo({
        url: `/pages/lanhu_gengduo/component?province=${currentProvince}&city=${currentCity ? currentCity.name : '全部'}`,
        success: () => {
          console.log('跳转到更多页面:', { province: currentProvince, city: currentCity?.name });
        },
        fail: (err) => {
          console.error('跳转失败:', err);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    },

    // 景区卡片点击事件
    onScenicCardTap: function() {
      const scenicInfo = this.data.currentScenicInfo;
      const currentProvince = this.data.provinces[0].name;

      // 跳转到景区详情页面
      wx.navigateTo({
        url: `/pages/lanhu_jingquxiangqing/component?scenicId=${scenicInfo.scenicId}&province=${currentProvince}&title=${scenicInfo.title}`,
        success: () => {
          console.log('跳转到景区详情页面:', scenicInfo);
        },
        fail: (err) => {
          console.error('跳转失败:', err);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    }
  }
});