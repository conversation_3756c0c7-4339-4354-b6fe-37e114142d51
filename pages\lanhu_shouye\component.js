
Component({
  properties: {},
  data: {
    currentAddress: '定位中...',
    isLoading: true,
    locationFailed: false,
    weather: {
      temp: '--',
      weather: '--'
    },
    backgroundImage: '/images/lanhu_shouye/FigmaDDSSlicePNG6878c0806cc44a4dc399352e0e756437.png',
    cityList: [
      { id: 0, name: '全部' },
      { id: 1, name: '南京' },
      { id: 2, name: '苏州' },
      { id: 3, name: '无锡' }
    ],
    selectedCityId: 0,
    provinces: [
      { name: '江苏', selected: true },
      { name: '陕西', selected: false },
      { name: '四川', selected: false },
      { name: '北京', selected: false },
      { name: '河南', selected: false },
      { name: '浙江', selected: false },
      { name: '广东', selected: false },
      { name: '山东', selected: false },
      { name: '湖北', selected: false },
      { name: '湖南', selected: false }
    ],
    selectedProvinceIndex: 0,
    isProvinceExpanded: false  // 省份列表是否展开
  },

  lifetimes: {
    attached: function() {
      this.getCurrentLocation();
    }
  },

  methods: {
    getCurrentLocation: function() {
      this.setData({ isLoading: true, locationFailed: false });
      
      wx.getLocation({
        type: 'gcj02',
        success: res => {
          const latitude = res.latitude;
          const longitude = res.longitude;
          this.getWeatherInfo(latitude, longitude);
          this.getAddressFromCoords(latitude, longitude)
            .then(result => {
              // 从返回的结果中提取需要的地址信息
              console.log(result)
              const address = result.address_component.province;
              this.setData({
                currentAddress: address,
                isLoading: false
              });
            })
            .catch(error => {
              console.error('获取地址失败:', error);
              this.setData({
                currentAddress: '获取地址失败',
                isLoading: false,
                locationFailed: true
              });
              wx.showToast({
                title: '获取地址失败，请检查网络后重试',
                icon: 'none',
                duration: 2000
              });
            });
        },
        fail: (err) => {
          console.error('获取位置失败:', err);
          this.setData({
            currentAddress: '获取位置失败',
            isLoading: false,
            locationFailed: true
          });
          // 判断是否是因为用户拒绝授权导致的失败
          if (err.errMsg.includes('auth deny')) {
            wx.showModal({
              title: '提示',
              content: '需要您的位置权限才能为您提供更好的服务，是否前往设置打开权限？',
              success: (res) => {
                if (res.confirm) {
                  wx.openSetting({
                    success: (settingRes) => {
                      if (settingRes.authSetting['scope.userLocation']) {
                        this.getCurrentLocation();
                      }
                    }
                  });
                }
              }
            });
          } else {
            wx.showToast({
              title: '获取位置失败，请检查网络后重试',
              icon: 'none',
              duration: 2000
            });
          }
        }
      });
    },

    getAddressFromCoords: function(latitude, longitude) {
      return new Promise((resolve, reject) => {
        wx.request({
          url: 'https://apis.map.qq.com/ws/geocoder/v1/',
          data: {
            location: `${latitude},${longitude}`,
            key: 'YE4BZ-SCTLT-OWMXO-V5SQP-LHAT7-PEBFS',
            get_poi: 0
          },
          success: res => {
            if (res.data && res.data.status === 0) {
              resolve(res.data.result);
            } else {
              reject(new Error(res.data.message || '获取地址信息失败'));
            }
          },
          fail: err => {
            reject(err);
          }
        });
      });
    },

    // 获取天气信息
    getWeatherInfo: function(latitude, longitude) {
      wx.request({
        url: 'https://apis.map.qq.com/ws/weather/v1/',
        data: {
          location: `${latitude},${longitude}`,
          key: 'YE4BZ-SCTLT-OWMXO-V5SQP-LHAT7-PEBFS'
        },
        success: res => {
          console.log('天气API响应:', res.data);
          if (res.data && res.data.status === 0 && res.data.result && res.data.result.realtime && res.data.result.realtime.length > 0) {
            const weatherInfo = res.data.result.realtime[0].infos;
            this.setData({
              weather: {
                temp: `${weatherInfo.temperature}℃`,
                weather: weatherInfo.weather
              }
            });
            console.log('天气信息获取成功:', weatherInfo);
          } else {
            console.error('获取天气失败:', res.data);
            this.setData({
              weather: {
                temp: '--℃',
                weather: '--'
              }
            });
          }
        },
        fail: err => {
          console.error('天气API请求失败:', err);
          this.setData({
            weather: {
              temp: '--℃',
              weather: '--'
            }
          });
        }
      });
    },

    // 重试获取位置
    retryGetLocation: function() {
      if (!this.data.isLoading) {
        this.getCurrentLocation();
      }
    },

    // 选择城市
    selectCity: function(e) {
      const cityId = e.currentTarget.dataset.cityId;
      this.setData({
        selectedCityId: cityId
      });
      
      // 获取选中的城市信息
      const selectedCity = this.data.cityList.find(city => city.id === cityId);
      console.log('选中城市:', selectedCity.name);
      
      // 这里可以添加选择城市后的其他操作
      // 例如：更新列表数据、发起请求等
    },
    
    // 选择省份
    selectProvince: function(e) {
      const index = e.currentTarget.dataset.index;
      const provinces = [...this.data.provinces];
      
      // 如果点击的不是第一个省份，则进行位置交换
      if (index !== 0) {
        // 保存点击的省份
        const clickedProvince = { ...provinces[index] };
        // 保存原第一个省份
        const firstProvince = { ...provinces[0] };
        
        // 更新选中状态
        clickedProvince.selected = true;
        firstProvince.selected = false;
        
        // 交换位置
        provinces[0] = clickedProvince;
        provinces[index] = firstProvince;
      }
      
      this.setData({
        provinces,
        selectedProvinceIndex: 0  // 选中的始终是第一个位置
      });
      
      console.log('选中省份:', provinces[0].name);
    },

    // 切换省份列表展开/收起状态
    toggleProvinceList: function() {
      this.setData({
        isProvinceExpanded: !this.data.isProvinceExpanded
      });
      console.log('省份列表展开状态:', this.data.isProvinceExpanded);
    }
  }
});