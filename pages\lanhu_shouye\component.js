
Component({
  properties: {},
  data: {
    currentAddress: '定位中...',
    isLoading: true,
    locationFailed: false,
    weather: {
      temp: '--',
      weather: '--'
    },
    backgroundImage: '/images/lanhu_shouye/FigmaDDSSlicePNG6878c0806cc44a4dc399352e0e756437.png',
    cityList: [
      { id: 0, name: '全部' },
      { id: 1, name: '南京' },
      { id: 2, name: '苏州' },
      { id: 3, name: '无锡' }
    ],
    selectedCityId: 0,
    provinces: [
      { name: '江苏', selected: true },
      { name: '陕西', selected: false },
      { name: '四川', selected: false },
      { name: '北京', selected: false },
      { name: '河南', selected: false },
      { name: '浙江', selected: false },
      { name: '广东', selected: false },
      { name: '山东', selected: false },
      { name: '湖北', selected: false },
      { name: '湖南', selected: false }
    ],
    selectedProvinceIndex: 0,
    showProvinceModal: false,  // 省份选择弹窗是否显示

    // 轮播图相关数据
    currentCarouselIndex: 0,  // 当前轮播图索引
    carouselAutoPlay: true,   // 是否自动播放
    carouselInterval: 3000,   // 自动播放间隔(毫秒)
    carouselTimer: null,      // 轮播定时器

    // 省份对应的轮播图数据
    provinceCarouselData: {
      '江苏': [
        {
          id: 1,
          image: '../../images/lanhu_shouye/FigmaDDSSlicePNG0f0a39259fd7ced310becc1918181999.png',
          title: '苏州园林',
          subtitle: '江南水乡的经典代表',
          scenicId: 'suzhou_garden'
        },
        {
          id: 2,
          image: '../../images/lanhu_shouye/FigmaDDSSlicePNG7ad30acb3ae297130d0e6d93dee06a05.png',
          title: '南京夫子庙',
          subtitle: '六朝古都的文化印记',
          scenicId: 'nanjing_fuzimiao'
        },
        {
          id: 3,
          image: '../../images/lanhu_shouye/FigmaDDSSlicePNG0f0a39259fd7ced310becc1918181999.png',
          title: '无锡太湖',
          subtitle: '烟波浩渺的太湖美景',
          scenicId: 'wuxi_taihu'
        }
      ],
      '陕西': [
        {
          id: 4,
          image: '../../images/lanhu_shouye/FigmaDDSSlicePNG7ad30acb3ae297130d0e6d93dee06a05.png',
          title: '兵马俑',
          subtitle: '世界第八大奇迹',
          scenicId: 'xian_bingmayong'
        },
        {
          id: 5,
          image: '../../images/lanhu_shouye/FigmaDDSSlicePNG0f0a39259fd7ced310becc1918181999.png',
          title: '华山',
          subtitle: '奇险天下第一山',
          scenicId: 'huashan'
        }
      ],
      '四川': [
        {
          id: 6,
          image: '../../images/lanhu_shouye/FigmaDDSSlicePNG0f0a39259fd7ced310becc1918181999.png',
          title: '九寨沟',
          subtitle: '人间仙境的童话世界',
          scenicId: 'jiuzhaigou'
        },
        {
          id: 7,
          image: '../../images/lanhu_shouye/FigmaDDSSlicePNG7ad30acb3ae297130d0e6d93dee06a05.png',
          title: '峨眉山',
          subtitle: '佛教名山金顶云海',
          scenicId: 'emeishan'
        }
      ],
      '北京': [
        {
          id: 8,
          image: '../../images/lanhu_shouye/FigmaDDSSlicePNG7ad30acb3ae297130d0e6d93dee06a05.png',
          title: '故宫',
          subtitle: '明清皇家宫殿建筑群',
          scenicId: 'beijing_gugong'
        },
        {
          id: 9,
          image: '../../images/lanhu_shouye/FigmaDDSSlicePNG0f0a39259fd7ced310becc1918181999.png',
          title: '长城',
          subtitle: '万里长城永不倒',
          scenicId: 'beijing_changcheng'
        }
      ],
      '河南': [
        {
          id: 10,
          image: '../../images/lanhu_shouye/FigmaDDSSlicePNG0f0a39259fd7ced310becc1918181999.png',
          title: '少林寺',
          subtitle: '禅宗祖庭武术圣地',
          scenicId: 'shaolin_temple'
        }
      ],
      '浙江': [
        {
          id: 11,
          image: '../../images/lanhu_shouye/FigmaDDSSlicePNG7ad30acb3ae297130d0e6d93dee06a05.png',
          title: '西湖',
          subtitle: '上有天堂下有苏杭',
          scenicId: 'hangzhou_xihu'
        }
      ],
      '广东': [
        {
          id: 12,
          image: '../../images/lanhu_shouye/FigmaDDSSlicePNG0f0a39259fd7ced310becc1918181999.png',
          title: '丹霞山',
          subtitle: '中国红石公园',
          scenicId: 'danxiashan'
        }
      ],
      '山东': [
        {
          id: 13,
          image: '../../images/lanhu_shouye/FigmaDDSSlicePNG7ad30acb3ae297130d0e6d93dee06a05.png',
          title: '泰山',
          subtitle: '五岳之首东岳泰山',
          scenicId: 'taishan'
        }
      ],
      '湖北': [
        {
          id: 14,
          image: '../../images/lanhu_shouye/FigmaDDSSlicePNG0f0a39259fd7ced310becc1918181999.png',
          title: '黄鹤楼',
          subtitle: '江南三大名楼之一',
          scenicId: 'huanghelou'
        }
      ],
      '湖南': [
        {
          id: 15,
          image: '../../images/lanhu_shouye/FigmaDDSSlicePNG7ad30acb3ae297130d0e6d93dee06a05.png',
          title: '张家界',
          subtitle: '奇峰异石的自然奇观',
          scenicId: 'zhangjiajie'
        }
      ]
    },

    // 当前省份的轮播图数据
    currentCarouselData: []
  },

  lifetimes: {
    attached: function() {
      this.getCurrentLocation();
      this.initCarousel();
    },

    detached: function() {
      // 清除轮播定时器
      if (this.data.carouselTimer) {
        clearInterval(this.data.carouselTimer);
      }
    }
  },

  methods: {
    getCurrentLocation: function() {
      this.setData({ isLoading: true, locationFailed: false });
      
      wx.getLocation({
        type: 'gcj02',
        success: res => {
          const latitude = res.latitude;
          const longitude = res.longitude;
          this.getWeatherInfo(latitude, longitude);
          this.getAddressFromCoords(latitude, longitude)
            .then(result => {
              // 从返回的结果中提取需要的地址信息
              console.log(result)
              const address = result.address_component.province;
              this.setData({
                currentAddress: address,
                isLoading: false
              });
            })
            .catch(error => {
              console.error('获取地址失败:', error);
              this.setData({
                currentAddress: '获取地址失败',
                isLoading: false,
                locationFailed: true
              });
              wx.showToast({
                title: '获取地址失败，请检查网络后重试',
                icon: 'none',
                duration: 2000
              });
            });
        },
        fail: (err) => {
          console.error('获取位置失败:', err);
          this.setData({
            currentAddress: '获取位置失败',
            isLoading: false,
            locationFailed: true
          });
          // 判断是否是因为用户拒绝授权导致的失败
          if (err.errMsg.includes('auth deny')) {
            wx.showModal({
              title: '提示',
              content: '需要您的位置权限才能为您提供更好的服务，是否前往设置打开权限？',
              success: (res) => {
                if (res.confirm) {
                  wx.openSetting({
                    success: (settingRes) => {
                      if (settingRes.authSetting['scope.userLocation']) {
                        this.getCurrentLocation();
                      }
                    }
                  });
                }
              }
            });
          } else {
            wx.showToast({
              title: '获取位置失败，请检查网络后重试',
              icon: 'none',
              duration: 2000
            });
          }
        }
      });
    },

    getAddressFromCoords: function(latitude, longitude) {
      return new Promise((resolve, reject) => {
        wx.request({
          url: 'https://apis.map.qq.com/ws/geocoder/v1/',
          data: {
            location: `${latitude},${longitude}`,
            key: 'YE4BZ-SCTLT-OWMXO-V5SQP-LHAT7-PEBFS',
            get_poi: 0
          },
          success: res => {
            if (res.data && res.data.status === 0) {
              resolve(res.data.result);
            } else {
              reject(new Error(res.data.message || '获取地址信息失败'));
            }
          },
          fail: err => {
            reject(err);
          }
        });
      });
    },

    // 获取天气信息
    getWeatherInfo: function(latitude, longitude) {
      wx.request({
        url: 'https://apis.map.qq.com/ws/weather/v1/',
        data: {
          location: `${latitude},${longitude}`,
          key: 'YE4BZ-SCTLT-OWMXO-V5SQP-LHAT7-PEBFS'
        },
        success: res => {
          console.log('天气API响应:', res.data);
          if (res.data && res.data.status === 0 && res.data.result && res.data.result.realtime && res.data.result.realtime.length > 0) {
            const weatherInfo = res.data.result.realtime[0].infos;
            this.setData({
              weather: {
                temp: `${weatherInfo.temperature}℃`,
                weather: weatherInfo.weather
              }
            });
            console.log('天气信息获取成功:', weatherInfo);
          } else {
            console.error('获取天气失败:', res.data);
            this.setData({
              weather: {
                temp: '--℃',
                weather: '--'
              }
            });
          }
        },
        fail: err => {
          console.error('天气API请求失败:', err);
          this.setData({
            weather: {
              temp: '--℃',
              weather: '--'
            }
          });
        }
      });
    },

    // 重试获取位置
    retryGetLocation: function() {
      if (!this.data.isLoading) {
        this.getCurrentLocation();
      }
    },

    // 选择城市
    selectCity: function(e) {
      const cityId = e.currentTarget.dataset.cityId;
      this.setData({
        selectedCityId: cityId
      });
      
      // 获取选中的城市信息
      const selectedCity = this.data.cityList.find(city => city.id === cityId);
      console.log('选中城市:', selectedCity.name);
      
      // 这里可以添加选择城市后的其他操作
      // 例如：更新列表数据、发起请求等
    },
    
    // 选择省份
    selectProvince: function(e) {
      const index = e.currentTarget.dataset.index;
      const provinces = [...this.data.provinces];
      
      // 如果点击的不是第一个省份，则进行位置交换
      if (index !== 0) {
        // 保存点击的省份
        const clickedProvince = { ...provinces[index] };
        // 保存原第一个省份
        const firstProvince = { ...provinces[0] };
        
        // 更新选中状态
        clickedProvince.selected = true;
        firstProvince.selected = false;
        
        // 交换位置
        provinces[0] = clickedProvince;
        provinces[index] = firstProvince;
      }
      
      this.setData({
        provinces,
        selectedProvinceIndex: 0  // 选中的始终是第一个位置
      });

      console.log('选中省份:', provinces[0].name);

      // 更新轮播图数据
      this.updateCarouselData(provinces[0].name);
    },

    // 打开省份选择弹窗
    toggleProvinceModal: function() {
      this.setData({
        showProvinceModal: true
      });
      console.log('打开省份选择弹窗');
    },

    // 关闭省份选择弹窗
    closeProvinceModal: function() {
      this.setData({
        showProvinceModal: false
      });
      console.log('关闭省份选择弹窗');
    },

    // 阻止事件冒泡
    stopPropagation: function() {
      // 阻止点击弹窗内容时关闭弹窗
    },

    // 从弹窗中选择省份
    selectProvinceFromModal: function(e) {
      const index = e.currentTarget.dataset.index;
      const provinces = [...this.data.provinces];

      // 如果点击的不是第一个省份，则进行位置交换
      if (index !== 0) {
        // 保存点击的省份
        const clickedProvince = { ...provinces[index] };
        // 保存原第一个省份
        const firstProvince = { ...provinces[0] };

        // 更新选中状态
        clickedProvince.selected = true;
        firstProvince.selected = false;

        // 交换位置
        provinces[0] = clickedProvince;
        provinces[index] = firstProvince;
      }

      this.setData({
        provinces,
        selectedProvinceIndex: 0,
        showProvinceModal: false  // 选择后关闭弹窗
      });

      console.log('从弹窗选中省份:', provinces[0].name);

      // 更新轮播图数据
      this.updateCarouselData(provinces[0].name);
    },

    // 初始化轮播图
    initCarousel: function() {
      const currentProvince = this.data.provinces[0].name;
      console.log('初始化轮播图，当前省份:', currentProvince);
      this.updateCarouselData(currentProvince);
    },

    // 更新轮播图数据
    updateCarouselData: function(provinceName) {
      const carouselData = this.data.provinceCarouselData[provinceName] || [];
      this.setData({
        currentCarouselData: carouselData,
        currentCarouselIndex: 0
      });

      // 重新启动自动播放
      this.startCarouselAutoPlay();
      console.log('更新轮播图数据:', provinceName, carouselData);
    },

    // 开始自动播放
    startCarouselAutoPlay: function() {
      // 清除之前的定时器
      if (this.data.carouselTimer) {
        clearInterval(this.data.carouselTimer);
      }

      // 如果数据少于2个，不启动自动播放
      if (!this.data.carouselAutoPlay || this.data.currentCarouselData.length < 2) {
        return;
      }

      const timer = setInterval(() => {
        this.nextCarouselImage();
      }, this.data.carouselInterval);

      this.setData({
        carouselTimer: timer
      });
    },

    // 停止自动播放
    stopCarouselAutoPlay: function() {
      if (this.data.carouselTimer) {
        clearInterval(this.data.carouselTimer);
        this.setData({
          carouselTimer: null
        });
      }
    },

    // 下一张图片
    nextCarouselImage: function() {
      const currentIndex = this.data.currentCarouselIndex;
      const totalImages = this.data.currentCarouselData.length;

      if (totalImages === 0) return;

      const nextIndex = (currentIndex + 1) % totalImages;
      this.setData({
        currentCarouselIndex: nextIndex
      });
    },

    // 上一张图片
    prevCarouselImage: function() {
      const currentIndex = this.data.currentCarouselIndex;
      const totalImages = this.data.currentCarouselData.length;

      if (totalImages === 0) return;

      const prevIndex = currentIndex === 0 ? totalImages - 1 : currentIndex - 1;
      this.setData({
        currentCarouselIndex: prevIndex
      });
    },

    // 轮播图点击事件
    onCarouselImageTap: function(e) {
      const index = e.currentTarget.dataset.index || this.data.currentCarouselIndex;
      const imageData = this.data.currentCarouselData[index];

      if (!imageData) return;

      // 跳转到景区详情页面
      wx.navigateTo({
        url: `/pages/lanhu_jingquxiangqing/component?scenicId=${imageData.scenicId}&province=${this.data.provinces[0].name}&title=${imageData.title}`,
        success: () => {
          console.log('跳转到景区详情页面:', imageData);
        },
        fail: (err) => {
          console.error('跳转失败:', err);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    },

    // 轮播图滑动事件
    onCarouselChange: function(e) {
      const current = e.detail.current;
      this.setData({
        currentCarouselIndex: current
      });

      // 重新启动自动播放
      this.startCarouselAutoPlay();
    },

    // 轮播图触摸开始
    onCarouselTouchStart: function() {
      this.stopCarouselAutoPlay();
    },

    // 轮播图触摸结束
    onCarouselTouchEnd: function() {
      this.startCarouselAutoPlay();
    }
  }
});